"""
路亚基地API接口
"""

from typing import List
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlmodel import Session
from app.db.session import get_db
from app.services import fishing_location_service
from app.schemas.fishing_location import (
    FishingLocationSearchParams,
    FishingLocationDetailResponse,
    FishingLocationListItem
)
from app.schemas.common import PaginatedResponseModel, ResponseModel

router = APIRouter()


@router.get("/search", response_model=PaginatedResponseModel[FishingLocationListItem])
async def search_fishing_locations(
    keyword: str = Query(None, description="搜索关键词"),
    province_code: str = Query(None, description="省份代码"),
    city_code: str = Query(None, description="城市代码"),
    area_code: str = Query(None, description="区县代码"),
    types: List[str] = Query(None, description="基地类型筛选"),
    fish_species: List[str] = Query(None, description="鱼种筛选"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db)
):
    """
    搜索路亚基地
    
    - **keyword**: 搜索关键词，会在基地名称、描述、联系方式中搜索
    - **province_code**: 省份代码筛选
    - **city_code**: 城市代码筛选
    - **area_code**: 区县代码筛选
    - **types**: 基地类型筛选（可多选）
    - **fish_species**: 鱼种筛选（可多选）
    - **page**: 页码，从1开始
    - **page_size**: 每页数量，最大100
    """
    search_params = FishingLocationSearchParams(
        keyword=keyword,
        province_code=province_code,
        city_code=city_code,
        area_code=area_code,
        types=types,
        fish_species=fish_species,
        page=page,
        page_size=page_size
    )
    
    locations, pagination = fishing_location_service.search_fishing_locations(db, search_params)
    
    return PaginatedResponseModel(
        code=200,
        message="success",
        data=locations,
        pagination=pagination
    )


@router.get("/{location_id}", response_model=ResponseModel[FishingLocationDetailResponse])
async def get_fishing_location_detail(
    location_id: int,
    db: Session = Depends(get_db)
):
    """
    获取路亚基地详情
    
    - **location_id**: 基地ID
    """
    location = fishing_location_service.get_fishing_location_detail(db, location_id)
    
    if not location:
        raise HTTPException(status_code=404, detail="路亚基地不存在")
    
    return ResponseModel(
        code=200,
        message="success",
        data=location
    )


@router.get("/region/list", response_model=PaginatedResponseModel[FishingLocationListItem])
async def get_fishing_locations_by_region(
    province_code: str = Query(None, description="省份代码"),
    city_code: str = Query(None, description="城市代码"),
    area_code: str = Query(None, description="区县代码"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db)
):
    """
    根据地区获取路亚基地列表
    
    - **province_code**: 省份代码
    - **city_code**: 城市代码
    - **area_code**: 区县代码
    - **page**: 页码，从1开始
    - **page_size**: 每页数量，最大100
    """
    locations, pagination = fishing_location_service.get_fishing_locations_by_region(
        db,
        province_code=province_code,
        city_code=city_code,
        area_code=area_code,
        page=page,
        page_size=page_size
    )
    
    return PaginatedResponseModel(
        code=200,
        message="success",
        data=locations,
        pagination=pagination
    )
