"""
路亚竿API接口
"""

from decimal import Decimal
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlmodel import Session
from app.db.session import get_db
from app.services import rod_service
from app.schemas.rod import (
    RodSearchParams,
    RodDetailResponse,
    RodListItem
)
from app.schemas.common import PaginatedResponseModel, ResponseModel

router = APIRouter()


@router.get("/search", response_model=PaginatedResponseModel[RodListItem])
async def search_rods(
    keyword: str = Query(None, description="搜索关键词"),
    company_id: int = Query(None, description="品牌ID"),
    series_id: int = Query(None, description="系列ID"),
    min_lure_wt: Decimal = Query(None, description="最小饵重"),
    max_lure_wt: Decimal = Query(None, description="最大饵重"),
    action: str = Query(None, description="调性"),
    power: str = Query(None, description="硬度"),
    reel_seat_type: str = Query(None, description="枪柄/直柄"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db)
):
    """
    搜索路亚竿
    
    - **keyword**: 搜索关键词，会在路亚竿名称、描述中搜索
    - **company_id**: 品牌ID筛选
    - **series_id**: 系列ID筛选
    - **min_lure_wt**: 最小饵重筛选
    - **max_lure_wt**: 最大饵重筛选
    - **action**: 调性筛选
    - **power**: 硬度筛选
    - **reel_seat_type**: 枪柄/直柄筛选
    - **page**: 页码，从1开始
    - **page_size**: 每页数量，最大100
    """
    search_params = RodSearchParams(
        keyword=keyword,
        company_id=company_id,
        series_id=series_id,
        min_lure_wt=min_lure_wt,
        max_lure_wt=max_lure_wt,
        action=action,
        power=power,
        reel_seat_type=reel_seat_type,
        page=page,
        page_size=page_size
    )
    
    rods, pagination = rod_service.search_rods(db, search_params)
    
    return PaginatedResponseModel(
        code=200,
        message="success",
        data=rods,
        pagination=pagination
    )


@router.get("/{rod_id}", response_model=ResponseModel[RodDetailResponse])
async def get_rod_detail(
    rod_id: int,
    db: Session = Depends(get_db)
):
    """
    获取路亚竿详情
    
    - **rod_id**: 路亚竿ID
    """
    rod = rod_service.get_rod_detail(db, rod_id)
    
    if not rod:
        raise HTTPException(status_code=404, detail="路亚竿不存在")
    
    return ResponseModel(
        code=200,
        message="success",
        data=rod
    )


@router.get("/uuid/{rod_uuid}", response_model=ResponseModel[RodDetailResponse])
async def get_rod_detail_by_uuid(
    rod_uuid: str,
    db: Session = Depends(get_db)
):
    """
    根据UUID获取路亚竿详情
    
    - **rod_uuid**: 路亚竿UUID
    """
    rod = rod_service.get_rod_by_uuid(db, rod_uuid)
    
    if not rod:
        raise HTTPException(status_code=404, detail="路亚竿不存在")
    
    return ResponseModel(
        code=200,
        message="success",
        data=rod
    )
