"""
数据库会话管理
配置 SQLModel 数据库引擎、会话本地化以及 FastAPI 中用于依赖注入的 get_db 函数
"""

from typing import Generator
from sqlmodel import Session, create_engine
from app.core.config import settings


# 创建数据库引擎
engine = create_engine(
    settings.database_url,
    echo=settings.debug,  # 在调试模式下打印SQL语句
    pool_pre_ping=True,   # 连接池预检查
    pool_recycle=300,     # 连接回收时间（秒）
    pool_size=5,          # 连接池大小
    max_overflow=10,      # 最大溢出连接数
)


def get_db() -> Generator[Session, None, None]:
    """
    获取数据库会话的依赖注入函数
    用于 FastAPI 的 Depends
    """
    with Session(engine) as session:
        try:
            yield session
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()
