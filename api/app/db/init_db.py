"""
数据库初始化脚本
用于在应用首次启动时初始化数据库，例如创建超级管理员用户或默认数据
"""

import logging
from sqlmodel import SQLModel
from app.db.session import engine
from app.core.config import settings

logger = logging.getLogger(__name__)


def create_tables() -> None:
    """创建所有数据表"""
    try:
        SQLModel.metadata.create_all(engine)
        logger.info("数据库表创建成功")
    except Exception as e:
        logger.error(f"数据库表创建失败: {e}")
        raise


def init_db() -> None:
    """初始化数据库"""
    logger.info("开始初始化数据库...")
    
    # 创建数据表
    create_tables()
    
    # 这里可以添加初始数据的创建逻辑
    # 例如：创建默认管理员用户、初始化基础数据等
    
    logger.info("数据库初始化完成")


if __name__ == "__main__":
    init_db()
