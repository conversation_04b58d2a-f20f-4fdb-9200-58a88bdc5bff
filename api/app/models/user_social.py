"""
用户第三方账号模型
对应数据库中的 user_social 表
"""

from typing import Optional
from sqlmodel import SQLModel, Field


class UserSocial(SQLModel, table=True):
    """第三方社交账号绑定资料表"""
    __tablename__ = "user_social"
    
    id: Optional[int] = Field(default=None, primary_key=True, description="主键ID")
    user_id: int = Field(foreign_key="user.id", description="用户ID")
    type: str = Field(max_length=255, description="三方类型")
    open_id: str = Field(max_length=64, description="OpenID")
    union_id: Optional[str] = Field(default=None, max_length=64, description="UnionId")
    nickname: str = Field(default="", max_length=30, description="昵称")
    avatar: str = Field(default="", max_length=255, description="头像")
    created_at: int = Field(default=0, description="创建时间")
    created_ip: str = Field(default="", max_length=20, description="用户IP")


class UserSocialCreate(SQLModel):
    """创建第三方账号绑定时的数据模型"""
    user_id: int = Field(description="用户ID")
    type: str = Field(max_length=255, description="三方类型")
    open_id: str = Field(max_length=64, description="OpenID")
    union_id: Optional[str] = Field(default=None, max_length=64, description="UnionId")
    nickname: str = Field(default="", max_length=30, description="昵称")
    avatar: str = Field(default="", max_length=255, description="头像")
    created_ip: str = Field(default="", max_length=20, description="用户IP")


class UserSocialUpdate(SQLModel):
    """更新第三方账号绑定时的数据模型"""
    nickname: Optional[str] = Field(default=None, max_length=30, description="昵称")
    avatar: Optional[str] = Field(default=None, max_length=255, description="头像")


class UserSocialRead(SQLModel):
    """读取第三方账号绑定时的数据模型"""
    id: int
    user_id: int
    type: str
    open_id: str
    union_id: Optional[str] = None
    nickname: str
    avatar: str
    created_at: int
    created_ip: str
