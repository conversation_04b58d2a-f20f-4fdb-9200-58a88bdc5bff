"""
数据模型模块
导入所有SQLModel模型，确保在创建数据库表时能够识别所有模型
"""

from .company import Company, CompanyCreate, CompanyUpdate, CompanyRead
from .fish import Fish, FishCreate, FishUpdate, FishRead
from .fishing_location import FishingLocation, FishingLocationCreate, FishingLocationUpdate, FishingLocationRead
from .series import Series, SeriesCreate, SeriesUpdate, SeriesRead
from .reel import Reel, ReelCreate, ReelUpdate, ReelRead
from .reel_item import ReelItem, ReelItemCreate, ReelItemUpdate, ReelItemRead
from .rod import Rod, RodCreate, RodUpdate, RodRead
from .rod_item import RodItem, RodItemCreate, RodItemUpdate, RodItemRead
from .user import User, UserCreate, UserUpdate, UserRead, UserLogin
from .user_social import UserSocial, UserSocialCreate, UserSocialUpdate, UserSocialRead

__all__ = [
    # Company models
    "Company", "CompanyCreate", "CompanyUpdate", "CompanyRead",
    # Fish models
    "Fish", "FishCreate", "FishUpdate", "FishRead",
    # FishingLocation models
    "FishingLocation", "FishingLocationCreate", "FishingLocationUpdate", "FishingLocationRead",
    # Series models
    "Series", "SeriesCreate", "SeriesUpdate", "SeriesRead",
    # Reel models
    "Reel", "ReelCreate", "ReelUpdate", "ReelRead",
    # ReelItem models
    "ReelItem", "ReelItemCreate", "ReelItemUpdate", "ReelItemRead",
    # Rod models
    "Rod", "RodCreate", "RodUpdate", "RodRead",
    # RodItem models
    "RodItem", "RodItemCreate", "RodItemUpdate", "RodItemRead",
    # User models
    "User", "UserCreate", "UserUpdate", "UserRead", "UserLogin",
    # UserSocial models
    "UserSocial", "UserSocialCreate", "UserSocialUpdate", "UserSocialRead",
]