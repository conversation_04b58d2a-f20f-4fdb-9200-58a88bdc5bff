"""
路亚竿型号模型
对应数据库中的 rod_item 表
"""

from typing import Optional, Dict, Any
from decimal import Decimal
from sqlmodel import SQLModel, Field, JSON, Column
from app.db.base import BaseModel


class RodItem(BaseModel, table=True):
    """路亚竿型号表"""
    __tablename__ = "rod_item"
    
    id: Optional[int] = Field(default=None, primary_key=True, description="主键ID")
    uuid: str = Field(max_length=64, description="UUID")
    rod_id: int = Field(foreign_key="rod.id", description="产品ID")
    name: str = Field(max_length=30, description="型号名称")
    sub_name: Optional[str] = Field(default=None, max_length=30, description="子名称")
    total_length: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON), description="总长")
    pieces: Optional[int] = Field(default=None, description="节数")
    min_lure_wt: Optional[Decimal] = Field(default=None, max_digits=3, decimal_places=1, description="最小饵重")
    max_lure_wt: Optional[Decimal] = Field(default=None, max_digits=3, decimal_places=1, description="最大饵重")
    min_line_wt: Optional[int] = Field(default=None, description="最小线拉力")
    max_line_wt: Optional[int] = Field(default=None, description="最大线拉力")
    min_line_pe: Optional[Decimal] = Field(default=None, max_digits=2, decimal_places=1, description="最小PE线号")
    max_line_pe: Optional[Decimal] = Field(default=None, max_digits=2, decimal_places=1, description="最大PE线号")
    weight: Optional[float] = Field(default=None, description="重量")
    action: Optional[str] = Field(default=None, max_length=20, description="调性")
    power: Optional[str] = Field(default=None, max_length=30, description="硬度")
    grip_material: Optional[str] = Field(default=None, max_length=255, description="手把材料")
    grip_length: Optional[Decimal] = Field(default=None, max_digits=3, decimal_places=1, description="手把长度")
    reel_seat_type: Optional[str] = Field(default=None, max_length=20, description="枪柄还是直柄")
    cover: Optional[str] = Field(default=None, max_length=255, description="型号图片")
    desc: Optional[str] = Field(default=None, description="型号说明")
    short_desc: Optional[str] = Field(default=None, max_length=50, description="一句话描述")
    release_year: Optional[int] = Field(default=None, description="发售年份")


class RodItemCreate(SQLModel):
    """创建路亚竿型号时的数据模型"""
    uuid: str = Field(max_length=64, description="UUID")
    rod_id: int = Field(description="产品ID")
    name: str = Field(max_length=30, description="型号名称")
    sub_name: Optional[str] = Field(default=None, max_length=30, description="子名称")
    total_length: Optional[Dict[str, Any]] = Field(default=None, description="总长")
    pieces: Optional[int] = Field(default=None, description="节数")
    min_lure_wt: Optional[Decimal] = Field(default=None, description="最小饵重")
    max_lure_wt: Optional[Decimal] = Field(default=None, description="最大饵重")
    min_line_wt: Optional[int] = Field(default=None, description="最小线拉力")
    max_line_wt: Optional[int] = Field(default=None, description="最大线拉力")
    min_line_pe: Optional[Decimal] = Field(default=None, description="最小PE线号")
    max_line_pe: Optional[Decimal] = Field(default=None, description="最大PE线号")
    weight: Optional[float] = Field(default=None, description="重量")
    action: Optional[str] = Field(default=None, max_length=20, description="调性")
    power: Optional[str] = Field(default=None, max_length=30, description="硬度")
    grip_material: Optional[str] = Field(default=None, max_length=255, description="手把材料")
    grip_length: Optional[Decimal] = Field(default=None, description="手把长度")
    reel_seat_type: Optional[str] = Field(default=None, max_length=20, description="枪柄还是直柄")
    cover: Optional[str] = Field(default=None, max_length=255, description="型号图片")
    desc: Optional[str] = Field(default=None, description="型号说明")
    short_desc: Optional[str] = Field(default=None, max_length=50, description="一句话描述")
    release_year: Optional[int] = Field(default=None, description="发售年份")


class RodItemUpdate(SQLModel):
    """更新路亚竿型号时的数据模型"""
    uuid: Optional[str] = Field(default=None, max_length=64, description="UUID")
    rod_id: Optional[int] = Field(default=None, description="产品ID")
    name: Optional[str] = Field(default=None, max_length=30, description="型号名称")
    sub_name: Optional[str] = Field(default=None, max_length=30, description="子名称")
    total_length: Optional[Dict[str, Any]] = Field(default=None, description="总长")
    pieces: Optional[int] = Field(default=None, description="节数")
    min_lure_wt: Optional[Decimal] = Field(default=None, description="最小饵重")
    max_lure_wt: Optional[Decimal] = Field(default=None, description="最大饵重")
    min_line_wt: Optional[int] = Field(default=None, description="最小线拉力")
    max_line_wt: Optional[int] = Field(default=None, description="最大线拉力")
    min_line_pe: Optional[Decimal] = Field(default=None, description="最小PE线号")
    max_line_pe: Optional[Decimal] = Field(default=None, description="最大PE线号")
    weight: Optional[float] = Field(default=None, description="重量")
    action: Optional[str] = Field(default=None, max_length=20, description="调性")
    power: Optional[str] = Field(default=None, max_length=30, description="硬度")
    grip_material: Optional[str] = Field(default=None, max_length=255, description="手把材料")
    grip_length: Optional[Decimal] = Field(default=None, description="手把长度")
    reel_seat_type: Optional[str] = Field(default=None, max_length=20, description="枪柄还是直柄")
    cover: Optional[str] = Field(default=None, max_length=255, description="型号图片")
    desc: Optional[str] = Field(default=None, description="型号说明")
    short_desc: Optional[str] = Field(default=None, max_length=50, description="一句话描述")
    release_year: Optional[int] = Field(default=None, description="发售年份")


class RodItemRead(SQLModel):
    """读取路亚竿型号时的数据模型"""
    id: int
    uuid: str
    rod_id: int
    name: str
    sub_name: Optional[str] = None
    total_length: Optional[Dict[str, Any]] = None
    pieces: Optional[int] = None
    min_lure_wt: Optional[Decimal] = None
    max_lure_wt: Optional[Decimal] = None
    min_line_wt: Optional[int] = None
    max_line_wt: Optional[int] = None
    min_line_pe: Optional[Decimal] = None
    max_line_pe: Optional[Decimal] = None
    weight: Optional[float] = None
    action: Optional[str] = None
    power: Optional[str] = None
    grip_material: Optional[str] = None
    grip_length: Optional[Decimal] = None
    reel_seat_type: Optional[str] = None
    cover: Optional[str] = None
    desc: Optional[str] = None
    short_desc: Optional[str] = None
    release_year: Optional[int] = None
    created_at: Optional[int] = None
    updated_at: Optional[int] = None
