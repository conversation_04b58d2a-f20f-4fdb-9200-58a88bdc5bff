"""
钓点/路亚基地模型
对应数据库中的 fishing_location 表
"""

from typing import Optional, List, Dict, Any
from sqlmodel import SQLModel, Field, JSON, Column


class FishingLocation(SQLModel, table=True):
    """钓点/路亚基地表"""
    __tablename__ = "fishing_location"
    
    id: Optional[int] = Field(default=None, primary_key=True, description="主键ID")
    name: str = Field(max_length=50, description="名称")
    types: Optional[List[str]] = Field(default=None, sa_column=Column(JSON), description="类型")
    province_code: Optional[str] = Field(default=None, max_length=15, description="省份")
    city_code: Optional[str] = Field(default=None, max_length=15, description="城市")
    area_code: Optional[str] = Field(default=None, max_length=15, description="区县")
    coordinates: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON), description="坐标")
    desc: Optional[str] = Field(default=None, description="介绍")
    contact: Optional[str] = Field(default=None, max_length=255, description="联系方式")
    fish_species: Optional[List[str]] = Field(default=None, sa_column=Column(JSON), description="鱼种")


class FishingLocationCreate(SQLModel):
    """创建钓点时的数据模型"""
    name: str = Field(max_length=50, description="名称")
    types: Optional[List[str]] = Field(default=None, description="类型")
    province_code: Optional[str] = Field(default=None, max_length=15, description="省份")
    city_code: Optional[str] = Field(default=None, max_length=15, description="城市")
    area_code: Optional[str] = Field(default=None, max_length=15, description="区县")
    coordinates: Optional[Dict[str, Any]] = Field(default=None, description="坐标")
    desc: Optional[str] = Field(default=None, description="介绍")
    contact: Optional[str] = Field(default=None, max_length=255, description="联系方式")
    fish_species: Optional[List[str]] = Field(default=None, description="鱼种")


class FishingLocationUpdate(SQLModel):
    """更新钓点时的数据模型"""
    name: Optional[str] = Field(default=None, max_length=50, description="名称")
    types: Optional[List[str]] = Field(default=None, description="类型")
    province_code: Optional[str] = Field(default=None, max_length=15, description="省份")
    city_code: Optional[str] = Field(default=None, max_length=15, description="城市")
    area_code: Optional[str] = Field(default=None, max_length=15, description="区县")
    coordinates: Optional[Dict[str, Any]] = Field(default=None, description="坐标")
    desc: Optional[str] = Field(default=None, description="介绍")
    contact: Optional[str] = Field(default=None, max_length=255, description="联系方式")
    fish_species: Optional[List[str]] = Field(default=None, description="鱼种")


class FishingLocationRead(SQLModel):
    """读取钓点时的数据模型"""
    id: int
    name: str
    types: Optional[List[str]] = None
    province_code: Optional[str] = None
    city_code: Optional[str] = None
    area_code: Optional[str] = None
    coordinates: Optional[Dict[str, Any]] = None
    desc: Optional[str] = None
    contact: Optional[str] = None
    fish_species: Optional[List[str]] = None
