"""
路亚竿模型
对应数据库中的 rod 表
"""

from typing import Optional
from sqlmodel import SQLModel, Field


class Rod(SQLModel, table=True):
    """路亚竿表"""
    __tablename__ = "rod"
    
    id: Optional[int] = Field(default=None, primary_key=True, description="主键ID")
    uuid: str = Field(max_length=64, unique=True, description="UUID")
    company_id: Optional[int] = Field(default=None, foreign_key="company.id", description="厂商ID")
    name: str = Field(max_length=50, description="名称")
    series_id: Optional[int] = Field(default=None, foreign_key="series.id", description="品牌系列ID")
    cover: Optional[str] = Field(default=None, max_length=255, description="封面图片")
    desc: Optional[str] = Field(default=None, description="描述")


class RodCreate(SQLModel):
    """创建路亚竿时的数据模型"""
    uuid: str = Field(max_length=64, description="UUID")
    company_id: Optional[int] = Field(default=None, description="厂商ID")
    name: str = Field(max_length=50, description="名称")
    series_id: Optional[int] = Field(default=None, description="品牌系列ID")
    cover: Optional[str] = Field(default=None, max_length=255, description="封面图片")
    desc: Optional[str] = Field(default=None, description="描述")


class RodUpdate(SQLModel):
    """更新路亚竿时的数据模型"""
    uuid: Optional[str] = Field(default=None, max_length=64, description="UUID")
    company_id: Optional[int] = Field(default=None, description="厂商ID")
    name: Optional[str] = Field(default=None, max_length=50, description="名称")
    series_id: Optional[int] = Field(default=None, description="品牌系列ID")
    cover: Optional[str] = Field(default=None, max_length=255, description="封面图片")
    desc: Optional[str] = Field(default=None, description="描述")


class RodRead(SQLModel):
    """读取路亚竿时的数据模型"""
    id: int
    uuid: str
    company_id: Optional[int] = None
    name: str
    series_id: Optional[int] = None
    cover: Optional[str] = None
    desc: Optional[str] = None
