"""
公司/品牌模型
对应数据库中的 company 表
"""

from typing import Optional
from sqlmodel import SQLModel, Field


class Company(SQLModel, table=True):
    """公司/品牌表"""
    __tablename__ = "company"
    
    id: Optional[int] = Field(default=None, primary_key=True, description="主键ID")
    name: str = Field(max_length=20, unique=True, description="公司简称")
    full_name: Optional[str] = Field(default=None, max_length=255, description="公司全称")
    logo: Optional[str] = Field(default=None, description="公司logo")
    desc: Optional[str] = Field(default=None, description="公司描述")
    country: Optional[str] = Field(default=None, max_length=30, description="公司国家")


class CompanyCreate(SQLModel):
    """创建公司时的数据模型"""
    name: str = Field(max_length=20, description="公司简称")
    full_name: Optional[str] = Field(default=None, max_length=255, description="公司全称")
    logo: Optional[str] = Field(default=None, description="公司logo")
    desc: Optional[str] = Field(default=None, description="公司描述")
    country: Optional[str] = Field(default=None, max_length=30, description="公司国家")


class CompanyUpdate(SQLModel):
    """更新公司时的数据模型"""
    name: Optional[str] = Field(default=None, max_length=20, description="公司简称")
    full_name: Optional[str] = Field(default=None, max_length=255, description="公司全称")
    logo: Optional[str] = Field(default=None, description="公司logo")
    desc: Optional[str] = Field(default=None, description="公司描述")
    country: Optional[str] = Field(default=None, max_length=30, description="公司国家")


class CompanyRead(SQLModel):
    """读取公司时的数据模型"""
    id: int
    name: str
    full_name: Optional[str] = None
    logo: Optional[str] = None
    desc: Optional[str] = None
    country: Optional[str] = None
