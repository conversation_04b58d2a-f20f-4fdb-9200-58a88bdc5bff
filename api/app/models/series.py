"""
品牌系列模型
对应数据库中的 series 表
"""

from typing import Optional
from sqlmodel import SQLModel, Field


class Series(SQLModel, table=True):
    """品牌系列表"""
    __tablename__ = "series"
    
    id: Optional[int] = Field(default=None, primary_key=True, description="主键ID")
    name: str = Field(max_length=30, description="品牌系列名称")
    company_id: Optional[int] = Field(default=None, foreign_key="company.id", description="厂商ID")
    desc: Optional[str] = Field(default=None, description="品牌系列介绍")


class SeriesCreate(SQLModel):
    """创建品牌系列时的数据模型"""
    name: str = Field(max_length=30, description="品牌系列名称")
    company_id: Optional[int] = Field(default=None, description="厂商ID")
    desc: Optional[str] = Field(default=None, description="品牌系列介绍")


class SeriesUpdate(SQLModel):
    """更新品牌系列时的数据模型"""
    name: Optional[str] = Field(default=None, max_length=30, description="品牌系列名称")
    company_id: Optional[int] = Field(default=None, description="厂商ID")
    desc: Optional[str] = Field(default=None, description="品牌系列介绍")


class SeriesRead(SQLModel):
    """读取品牌系列时的数据模型"""
    id: int
    name: str
    company_id: Optional[int] = None
    desc: Optional[str] = None
