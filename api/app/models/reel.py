"""
渔轮模型
对应数据库中的 reel 表
"""

from typing import Optional
from sqlmodel import SQLModel, Field


class Reel(SQLModel, table=True):
    """渔轮表"""
    __tablename__ = "reel"
    
    id: Optional[int] = Field(default=None, primary_key=True, description="主键ID")
    uuid: str = Field(max_length=64, unique=True, description="UUID")
    company_id: Optional[int] = Field(default=None, foreign_key="company.id", description="厂商ID")
    name: str = Field(max_length=50, description="名称")
    type: str = Field(max_length=20, description="渔轮类型")
    series_id: Optional[int] = Field(default=None, foreign_key="series.id", description="品牌系列ID")
    cover: Optional[str] = Field(default=None, max_length=255, description="封面图片")
    desc: Optional[str] = Field(default=None, description="描述")


class ReelCreate(SQLModel):
    """创建渔轮时的数据模型"""
    uuid: str = Field(max_length=64, description="UUID")
    company_id: Optional[int] = Field(default=None, description="厂商ID")
    name: str = Field(max_length=50, description="名称")
    type: str = Field(max_length=20, description="渔轮类型")
    series_id: Optional[int] = Field(default=None, description="品牌系列ID")
    cover: Optional[str] = Field(default=None, max_length=255, description="封面图片")
    desc: Optional[str] = Field(default=None, description="描述")


class ReelUpdate(SQLModel):
    """更新渔轮时的数据模型"""
    uuid: Optional[str] = Field(default=None, max_length=64, description="UUID")
    company_id: Optional[int] = Field(default=None, description="厂商ID")
    name: Optional[str] = Field(default=None, max_length=50, description="名称")
    type: Optional[str] = Field(default=None, max_length=20, description="渔轮类型")
    series_id: Optional[int] = Field(default=None, description="品牌系列ID")
    cover: Optional[str] = Field(default=None, max_length=255, description="封面图片")
    desc: Optional[str] = Field(default=None, description="描述")


class ReelRead(SQLModel):
    """读取渔轮时的数据模型"""
    id: int
    uuid: str
    company_id: Optional[int] = None
    name: str
    type: str
    series_id: Optional[int] = None
    cover: Optional[str] = None
    desc: Optional[str] = None
