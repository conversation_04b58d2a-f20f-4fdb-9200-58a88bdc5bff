"""
渔轮型号模型
对应数据库中的 reel_item 表
"""

from typing import Optional, List, Dict, Any
from decimal import Decimal
from sqlmodel import SQLModel, Field, JSON, Column
from app.db.base import BaseModel


class ReelItem(BaseModel, table=True):
    """渔轮型号表"""
    __tablename__ = "reel_item"
    
    id: Optional[int] = Field(default=None, primary_key=True, description="主键ID")
    uuid: str = Field(max_length=64, description="UUID")
    reel_id: int = Field(foreign_key="reel.id", description="渔轮ID")
    name: str = Field(max_length=30, description="型号名称")
    gear_ratio: Optional[Decimal] = Field(default=None, max_digits=2, decimal_places=1, description="速比")
    cover: Optional[str] = Field(default=None, max_length=255, description="型号图片")
    desc: Optional[str] = Field(default=None, description="型号说明")
    short_desc: Optional[str] = Field(default=None, max_length=50, description="一句话描述")
    best_drag: Optional[str] = Field(default=None, max_length=255, description="实用卸力kg")
    max_drag: Optional[Decimal] = Field(default=None, max_digits=3, decimal_places=1, description="最大卸力kg")
    weight: Optional[int] = Field(default=None, description="重量g")
    spool: Optional[str] = Field(default=None, max_length=30, description="线杯参数")
    bearings: Optional[str] = Field(default=None, max_length=10, description="轴承数")
    nylon_capacity: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON), description="尼龙线容量")
    pe_capacity: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON), description="PE线容量")
    fluorocarbon_capacity: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON), description="碳氟线容量")
    line_per_crank: Optional[int] = Field(default=None, description="手把转一圈最大收线长")
    handle_length: Optional[int] = Field(default=None, description="摇臂长度")
    handle_position: Optional[str] = Field(default=None, max_length=10, description="摇臂位置LEFT/RIGHT")
    release_year: Optional[int] = Field(default=None, description="发售日期")


class ReelItemCreate(SQLModel):
    """创建渔轮型号时的数据模型"""
    uuid: str = Field(max_length=64, description="UUID")
    reel_id: int = Field(description="渔轮ID")
    name: str = Field(max_length=30, description="型号名称")
    gear_ratio: Optional[Decimal] = Field(default=None, description="速比")
    cover: Optional[str] = Field(default=None, max_length=255, description="型号图片")
    desc: Optional[str] = Field(default=None, description="型号说明")
    short_desc: Optional[str] = Field(default=None, max_length=50, description="一句话描述")
    best_drag: Optional[str] = Field(default=None, max_length=255, description="实用卸力kg")
    max_drag: Optional[Decimal] = Field(default=None, description="最大卸力kg")
    weight: Optional[int] = Field(default=None, description="重量g")
    spool: Optional[str] = Field(default=None, max_length=30, description="线杯参数")
    bearings: Optional[str] = Field(default=None, max_length=10, description="轴承数")
    nylon_capacity: Optional[Dict[str, Any]] = Field(default=None, description="尼龙线容量")
    pe_capacity: Optional[Dict[str, Any]] = Field(default=None, description="PE线容量")
    fluorocarbon_capacity: Optional[Dict[str, Any]] = Field(default=None, description="碳氟线容量")
    line_per_crank: Optional[int] = Field(default=None, description="手把转一圈最大收线长")
    handle_length: Optional[int] = Field(default=None, description="摇臂长度")
    handle_position: Optional[str] = Field(default=None, max_length=10, description="摇臂位置LEFT/RIGHT")
    release_year: Optional[int] = Field(default=None, description="发售日期")


class ReelItemUpdate(SQLModel):
    """更新渔轮型号时的数据模型"""
    uuid: Optional[str] = Field(default=None, max_length=64, description="UUID")
    reel_id: Optional[int] = Field(default=None, description="渔轮ID")
    name: Optional[str] = Field(default=None, max_length=30, description="型号名称")
    gear_ratio: Optional[Decimal] = Field(default=None, description="速比")
    cover: Optional[str] = Field(default=None, max_length=255, description="型号图片")
    desc: Optional[str] = Field(default=None, description="型号说明")
    short_desc: Optional[str] = Field(default=None, max_length=50, description="一句话描述")
    best_drag: Optional[str] = Field(default=None, max_length=255, description="实用卸力kg")
    max_drag: Optional[Decimal] = Field(default=None, description="最大卸力kg")
    weight: Optional[int] = Field(default=None, description="重量g")
    spool: Optional[str] = Field(default=None, max_length=30, description="线杯参数")
    bearings: Optional[str] = Field(default=None, max_length=10, description="轴承数")
    nylon_capacity: Optional[Dict[str, Any]] = Field(default=None, description="尼龙线容量")
    pe_capacity: Optional[Dict[str, Any]] = Field(default=None, description="PE线容量")
    fluorocarbon_capacity: Optional[Dict[str, Any]] = Field(default=None, description="碳氟线容量")
    line_per_crank: Optional[int] = Field(default=None, description="手把转一圈最大收线长")
    handle_length: Optional[int] = Field(default=None, description="摇臂长度")
    handle_position: Optional[str] = Field(default=None, max_length=10, description="摇臂位置LEFT/RIGHT")
    release_year: Optional[int] = Field(default=None, description="发售日期")


class ReelItemRead(SQLModel):
    """读取渔轮型号时的数据模型"""
    id: int
    uuid: str
    reel_id: int
    name: str
    gear_ratio: Optional[Decimal] = None
    cover: Optional[str] = None
    desc: Optional[str] = None
    short_desc: Optional[str] = None
    best_drag: Optional[str] = None
    max_drag: Optional[Decimal] = None
    weight: Optional[int] = None
    spool: Optional[str] = None
    bearings: Optional[str] = None
    nylon_capacity: Optional[Dict[str, Any]] = None
    pe_capacity: Optional[Dict[str, Any]] = None
    fluorocarbon_capacity: Optional[Dict[str, Any]] = None
    line_per_crank: Optional[int] = None
    handle_length: Optional[int] = None
    handle_position: Optional[str] = None
    release_year: Optional[int] = None
    created_at: Optional[int] = None
    updated_at: Optional[int] = None
