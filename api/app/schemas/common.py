"""
通用响应模式
定义API响应的通用数据结构
"""

from typing import Generic, TypeVar, Optional, List, Any
from pydantic import BaseModel, Field

T = TypeVar('T')


class ResponseModel(BaseModel, Generic[T]):
    """通用响应模型"""
    code: int = Field(default=200, description="响应状态码")
    message: str = Field(default="success", description="响应消息")
    data: Optional[T] = Field(default=None, description="响应数据")


class PaginationModel(BaseModel):
    """分页信息模型"""
    page: int = Field(ge=1, description="当前页码")
    page_size: int = Field(ge=1, le=100, description="每页数量")
    total: int = Field(ge=0, description="总记录数")
    total_pages: int = Field(ge=0, description="总页数")


class PaginatedResponseModel(BaseModel, Generic[T]):
    """分页响应模型"""
    code: int = Field(default=200, description="响应状态码")
    message: str = Field(default="success", description="响应消息")
    data: List[T] = Field(default_factory=list, description="响应数据列表")
    pagination: PaginationModel = Field(description="分页信息")


class SearchParams(BaseModel):
    """搜索参数基类"""
    keyword: Optional[str] = Field(default=None, description="搜索关键词")
    page: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=20, ge=1, le=100, description="每页数量")


class ErrorModel(BaseModel):
    """错误响应模型"""
    code: int = Field(description="错误代码")
    message: str = Field(description="错误消息")
    detail: Optional[Any] = Field(default=None, description="错误详情")
