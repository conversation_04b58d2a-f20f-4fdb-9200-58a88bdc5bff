"""
应用启动和关闭事件处理
"""

import logging
from fastapi import FastAPI
from app.core.logging import setup_logging
from app.db.init_db import init_db

logger = logging.getLogger(__name__)


def create_start_app_handler(app: FastAPI):
    """创建应用启动事件处理器"""
    
    async def start_app() -> None:
        """应用启动时执行的操作"""
        logger.info("应用启动中...")
        
        # 初始化数据库
        try:
            # 暂时注释掉数据库初始化以排查问题
            # init_db()
            logger.info("数据库初始化跳过")
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
        
        logger.info("应用启动完成")
    
    return start_app


def create_stop_app_handler(app: FastAPI):
    """创建应用关闭事件处理器"""
    
    async def stop_app() -> None:
        """应用关闭时执行的操作"""
        logger.info("应用关闭中...")
        
        # 这里可以添加清理资源的逻辑
        # 例如：关闭数据库连接池、清理缓存等
        
        logger.info("应用已关闭")
    
    return stop_app


def setup_app_events(app: FastAPI) -> None:
    """设置应用事件处理器"""
    
    # 设置启动事件
    app.add_event_handler("startup", create_start_app_handler(app))
    
    # 设置关闭事件
    app.add_event_handler("shutdown", create_stop_app_handler(app))
    
    logger.info("应用事件处理器已设置完成")
