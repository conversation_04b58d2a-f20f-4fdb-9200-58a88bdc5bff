Metadata-Version: 2.3
Name: luyabaike-api
Version: 0.1.0
Summary: 路亚百科API - 为路亚爱好者提供装备和基地信息查询
Author: Your Name
Author-email: <EMAIL>
Requires-Python: >=3.13.5,<4.0.0
Classifier: Programming Language :: Python :: 3
Requires-Dist: alembic (>=1.14.0,<2.0.0)
Requires-Dist: cryptography (>=44.0.0,<45.0.0)
Requires-Dist: fastapi (==0.116.1)
Requires-Dist: passlib[bcrypt] (>=1.7.4,<2.0.0)
Requires-Dist: pydantic (==2.11.7)
Requires-Dist: pydantic-settings (==2.10.1)
Requires-Dist: pymysql (>=1.1.1,<2.0.0)
Requires-Dist: python-dotenv (>=1.0.1,<2.0.0)
Requires-Dist: python-jose[cryptography] (>=3.3.0,<4.0.0)
Requires-Dist: python-json-logger (>=2.0.7,<3.0.0)
Requires-Dist: python-multipart (>=0.0.20,<0.0.21)
Requires-Dist: sqlmodel (==0.0.24)
Requires-Dist: uvicorn[standard] (==0.35.0)
Description-Content-Type: text/markdown

# 路亚百科API

路亚百科API是一个为路亚爱好者提供路亚装备和基地信息查询的后端服务。

## 功能特性

- 🎣 **路亚基地查询** - 提供全国路亚基地的搜索和详情查询
- 🎯 **路亚竿信息** - 包含各品牌路亚竿的详细参数和型号信息
- 🎪 **渔轮数据** - 提供渔轮的技术参数和性能数据
- 🔍 **智能搜索** - 支持关键词搜索和多条件筛选
- 📱 **RESTful API** - 标准的REST API接口设计
- 📚 **自动文档** - 基于OpenAPI的自动API文档生成

## 技术栈

- **Python 3.13.5** - 编程语言
- **FastAPI 0.116.1** - Web框架
- **SQLModel 0.0.24** - ORM框架
- **Pydantic 2.11.7** - 数据验证
- **MySQL 8.0+** - 数据库
- **Poetry** - 依赖管理
- **Uvicorn** - ASGI服务器

## 项目结构

```
api/
├── app/                    # 应用核心代码
│   ├── api/               # API接口层
│   │   └── v1/           # API版本1
│   │       ├── endpoints/ # 具体接口实现
│   │       └── api.py    # 路由聚合
│   ├── core/             # 核心配置
│   │   ├── config.py     # 应用配置
│   │   ├── logging.py    # 日志配置
│   │   ├── exceptions.py # 异常处理
│   │   └── events.py     # 应用事件
│   ├── db/               # 数据库层
│   │   ├── session.py    # 数据库会话
│   │   ├── base.py       # 基础模型
│   │   └── init_db.py    # 数据库初始化
│   ├── models/           # 数据模型
│   ├── schemas/          # API数据模型
│   ├── crud/             # 数据访问层
│   ├── services/         # 业务逻辑层
│   ├── dependencies/     # 依赖注入
│   ├── utils/            # 工具函数
│   └── main.py           # 应用入口
├── tests/                # 测试代码
├── docs/                 # 项目文档
├── logs/                 # 日志文件
├── .env                  # 环境变量
├── pyproject.toml        # 项目配置
├── Dockerfile            # Docker配置
└── README.md             # 项目说明
```

## 快速开始

### 环境要求

- Python 3.13.5+
- MySQL 8.0+
- Poetry

### 安装依赖

```bash
# 安装Poetry（如果未安装）
curl -sSL https://install.python-poetry.org | python3 -

# 安装项目依赖
poetry install
```

### 配置环境

1. 复制环境变量文件：
```bash
cp .env.example .env
```

2. 修改 `.env` 文件中的配置：
```env
DATABASE_HOST=localhost
DATABASE_USER=root
DATABASE_PASSWORD=your_password
DATABASE_NAME=luyabaike
```

### 数据库设置

1. 创建数据库：
```sql
CREATE DATABASE luyabaike CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 导入数据表结构：
```bash
mysql -u root -p luyabaike < docs/luyabaike.sql
```

### 运行应用

```bash
# 开发模式运行
poetry run python app/main.py

# 或使用uvicorn直接运行
poetry run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

应用启动后，访问以下地址：

- API文档：http://localhost:8000/api/v1/docs
- ReDoc文档：http://localhost:8000/api/v1/redoc
- 健康检查：http://localhost:8000/health

## API接口

### 路亚基地

- `GET /api/v1/fishing-locations/search` - 搜索路亚基地
- `GET /api/v1/fishing-locations/{id}` - 获取基地详情
- `GET /api/v1/fishing-locations/region/list` - 按地区获取基地

### 路亚竿

- `GET /api/v1/rods/search` - 搜索路亚竿
- `GET /api/v1/rods/{id}` - 获取路亚竿详情
- `GET /api/v1/rods/uuid/{uuid}` - 根据UUID获取详情

### 渔轮

- `GET /api/v1/reels/search` - 搜索渔轮
- `GET /api/v1/reels/{id}` - 获取渔轮详情
- `GET /api/v1/reels/uuid/{uuid}` - 根据UUID获取详情

## 开发指南

### 代码规范

项目使用以下工具确保代码质量：

- **Black** - 代码格式化
- **isort** - 导入排序
- **flake8** - 代码检查
- **mypy** - 类型检查

运行代码检查：
```bash
poetry run black .
poetry run isort .
poetry run flake8 .
poetry run mypy .
```

### 测试

```bash
# 运行所有测试
poetry run pytest

# 运行特定测试
poetry run pytest tests/api/test_fishing_locations.py

# 生成测试覆盖率报告
poetry run pytest --cov=app
```

## 部署

### Docker部署

```bash
# 构建镜像
docker build -t luyabaike-api .

# 运行容器
docker run -d \
  --name luyabaike-api \
  -p 8000:8000 \
  -e DATABASE_HOST=your_db_host \
  -e DATABASE_PASSWORD=your_password \
  luyabaike-api
```

### 生产环境

生产环境建议使用以下配置：

1. 使用Gunicorn作为WSGI服务器
2. 配置Nginx作为反向代理
3. 使用Redis作为缓存
4. 配置日志轮转
5. 设置监控和告警

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 项目Issues：[GitHub Issues](https://github.com/your-repo/luyabaike-api/issues)
- 邮箱：<EMAIL>

