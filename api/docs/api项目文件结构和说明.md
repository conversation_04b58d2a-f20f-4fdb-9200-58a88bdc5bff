
使用以下项目结构。

```
.
├── .env                  # 环境变量文件 (用于本地开发)
├── .gitignore            # Git 忽略文件
├── Dockerfile            # Docker 容器化文件
├── pyproject.toml        # Poetry/PDM 依赖管理 (或 requirements.txt)
├── README.md             # 项目说明文档
└── app/                  # 核心应用代码目录
    ├── __init__.py       # 使 app 成为一个 Python 包
    ├── main.py           # FastAPI 应用的入口点
    ├── api/              # API 接口定义层
    │   ├── __init__.py
    │   ├── v1/           # API 版本控制 (例如：v1, v2)
    │   │   ├── __init__.py
    │   │   ├── endpoints/ # 各个模块的API接口
    │   │   │   ├── __init__.py
    │   │   │   ├── users.py      # 用户相关的API接口
    │   │   │   ├── items.py      # 物品相关的API接口
    │   │   │   └── auth.py       # 认证相关的API接口
    │   │   └── api.py          # 聚合 v1 版本的所有 APIRouter
    │   └── router.py         # 聚合所有 API 版本的 APIRouter
    ├── core/             # 核心配置和通用功能
    │   ├── __init__.py
    │   ├── config.py         # 项目配置 (使用 Pydantic BaseSettings)
    │   ├── security.py       # 认证/授权相关的工具函数 (例如：JWT编码解码、密码哈希)
    │   ├── logging.py        # 日志配置
    │   ├── events.py         # 应用启动/关闭事件处理函数
    │   └── exceptions.py     # 自定义异常和异常处理器
    ├── db/               # 数据库相关配置和操作
    │   ├── __init__.py
    │   ├── session.py        # 数据库会话管理 (SQLModel Engine, SessionLocal, get_db 依赖)
    │   ├── base.py           # SQLModel ORM 模型基类
    │   └── init_db.py        # 数据库初始化脚本 (例如：创建超级用户)
    ├── models/           # SQLModel ORM 模型定义 (与数据库表结构对应)
    │   ├── __init__.py
    │   ├── user.py           # 用户模型
    │   └── item.py           # 物品模型
    ├── schemas/          # Pydantic 模型定义 (请求体、响应体、数据验证)
    │   ├── __init__.py
    │   ├── user.py           # 用户相关的 Pydantic 模型
    │   ├── item.py           # 物品相关的 Pydantic 模型
    │   └── auth.py           # 认证相关的 Pydantic 模型
    ├── crud/             # 数据操作层 (Create, Read, Update, Delete)
    │   │                 # 仅包含直接与数据库交互的函数，不涉及业务逻辑
    │   ├── __init__.py
    │   ├── base.py           # CRUD 通用基类或通用函数
    │   ├── user.py           # 用户 CRUD 操作
    │   └── item.py           # 物品 CRUD 操作
    ├── services/         # 业务逻辑层 (Business Logic Layer)
    │   │                 # 封装复杂业务逻辑，协调 crud 层和外部服务
    │   ├── __init__.py
    │   ├── user_service.py   # 用户业务逻辑
    │   └── item_service.py   # 物品业务逻辑
    ├── dependencies/     # FastAPI 依赖注入函数 (get_db, get_current_user 等)
    │   ├── __init__.py
    │   └── common.py
    └── utils/            # 通用工具函数、辅助函数 (不属于以上任何一层)
        ├── __init__.py
        └── helpers.py        # 例如：邮件发送、数据格式化等
└── tests/                # 测试目录
    ├── __init__.py
    ├── conftest.py       # Pytest 共享夹具
    ├── api/
    │   ├── __init__.py
    │   ├── test_users.py
    │   └── test_items.py
    ├── crud/
    │   └── test_crud_users.py
    └── services/
        └── test_user_service.py
```

### 目录和文件说明：

1.  **`app/`**: 核心应用代码的根目录。
    *   **`main.py`**: FastAPI 应用的入口文件。在这里创建 `FastAPI` 实例，加载配置，注册路由，设置事件处理器等。
    *   **`api/`**: 存放 API 路由定义。
        *   **`v1/`**: 用于 API 版本控制，建议从一开始就考虑。
            *   **`endpoints/`**: 存放不同资源（如用户、物品、认证）的 `APIRouter` 定义。每个文件负责一个或一组相关资源的接口。
            *   **`api.py`**: 在每个版本目录下，聚合该版本的所有 `endpoints` 中的 `APIRouter`。
        *   **`router.py`**: 聚合所有 API 版本的 `APIRouter`（例如，将 `v1` 和 `v2` 聚合起来）。
    *   **`core/`**: 项目的核心配置和通用功能。
        *   **`config.py`**: 使用 Pydantic 的 `BaseSettings` 来管理配置，可以从环境变量、`.env` 文件加载，确保类型安全。
        *   **`database.py`**: 将数据库相关的配置从`config.py`拆分出来。
        *   **`security.py`**: 存放与认证和授权相关的工具函数，如密码哈希、JWT 令牌的生成和验证等。
        *   **`logging.py`**: 日志系统配置，包括日志级别、输出格式和目标（文件、控制台等）。
        *   **`events.py`**: 定义 FastAPI 应用启动 (`startup`) 和关闭 (`shutdown`) 时的事件处理函数，例如数据库连接初始化/关闭。
        *   **`exceptions.py`**: 定义自定义异常类和 FastAPI 的异常处理器，统一错误响应格式。
    *   **`db/`**: 数据库相关的配置和操作。
        *   **`session.py`**: 配置 SQLModel 数据库引擎、会话本地化 (`SessionLocal`) 以及 FastAPI 中用于依赖注入的 `get_db` 函数。
        *   **`base.py`**: 定义 SQLModel ORM 模型的基础类，方便所有模型继承。
        *   **`init_db.py`**: 一个可选的脚本，用于在应用首次启动时初始化数据库，例如创建超级管理员用户或默认数据。
    *   **`models/`**: 定义 SQLModel ORM 模型，这些模型直接映射到数据库的表结构。
    *   **`schemas/`**: 定义 Pydantic 模型，用于数据验证、请求体、响应体和数据序列化/反序列化。它们是 API 接口的数据契约。
    *   **`crud/`**: 数据操作层（Data Access Layer - DAL）。这层代码只负责与数据库进行直接交互，执行创建、读取、更新、删除（CRUD）操作。
        *   **`base.py`**: 可以包含一个通用的 CRUD 操作基类，用于减少重复代码。
    *   **`services/`**: 业务逻辑层（Business Logic Layer）。这一层负责封装复杂的业务逻辑，协调 `crud` 层和外部服务，处理跨多个模型的逻辑。它是你应用的核心业务逻辑所在。
    *   **`dependencies/`**: 存放 FastAPI 的依赖注入函数，如 `get_db`（获取数据库会话）、`get_current_user`（获取当前认证用户）等。
    *   **`utils/`**: 存放通用的辅助函数或工具类，这些函数不属于以上任何一层，但在项目中会多次使用（例如，邮件发送、文件上传处理等）。

2.  **`tests/`**: 存放所有测试代码，通常按照应用模块结构进行镜像。
    *   **`conftest.py`**: Pytest 的配置文件，可以定义全局的测试夹具（fixtures），如测试数据库连接、测试客户端等。

3.  **项目根目录下的文件**:
    *   **`.env`**: 存放敏感信息或环境变量，只在本地开发中使用，不应提交到版本控制。
    *   **`.gitignore`**: 忽略不需要提交到 Git 仓库的文件和目录（如 `.env`, `__pycache__`, `.pytest_cache` 等）。
    *   **`Dockerfile`**: 用于将应用打包成 Docker 镜像，便于部署。
    *   **`pyproject.toml`**: 推荐使用 Poetry 或 PDM 进行依赖管理，更现代化。如果使用 `pip`，则为 `requirements.txt`。
    *   **`README.md`**: 项目的说明文档，包括安装、运行、API 文档等信息。

### 关键实践理念：

*   **分离关注点 (Separation of Concerns)**:
    *   **`api`**: 仅处理 HTTP 请求/响应，路由，参数解析和调用业务逻辑层。
    *   **`schemas`**: 仅定义数据结构和验证规则。
    *   **`crud`**: 仅进行数据库操作，不含业务逻辑。
    *   **`services`**: 包含核心业务逻辑，协调 `crud` 层，是你的“大脑”。
    *   **`models`**: 仅定义数据库模型。
*   **依赖注入 (Dependency Injection)**: FastAPI 的核心特性。通过 `dependencies/common.py` 中的函数来集中管理依赖，确保代码的松耦合和可测试性。
*   **版本控制 (API Versioning)**: `api/v1/` 目录结构为未来的 API 版本迭代提供了清晰的路径。
*   **配置管理 (Configuration Management)**: 使用 Pydantic `BaseSettings` 从环境变量加载配置，提供类型检查和默认值，使配置更健壮。
*   **测试 (Testing)**: 编写单元测试 (`crud`, `services`) 和集成测试 (`api`)，确保代码质量和功能正确性。`tests` 目录结构应与 `app` 目录结构对应，方便查找和维护。
*   **日志 (Logging)**: 良好的日志系统有助于调试和监控应用。
*   **错误处理 (Error Handling)**: 统一的异常处理机制可以提供友好的错误信息给前端。
*   **容器化 (Containerization)**: Dockerfile 使得部署环境一致且便捷。

这个结构为中等规模的FastAPI项目提供了坚实的基础，可以随着项目的发展轻松扩展，同时保持代码的清晰和可维护性。