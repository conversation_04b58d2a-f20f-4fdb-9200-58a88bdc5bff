## 项目介绍
这是一个路亚相关的应用，主要功能是为路亚爱好者提供路亚竿、路亚渔轮、路亚饵、鱼线、鱼钩等路亚装备和路亚基地信息查询。
项目规模为中等，不要因为开始的项目规模小而按照小项目设计管理。

### 项目结构
- frontend 前端，使用vuejs技术栈，所有开发依赖包以vuejs的最新版为基准，全部使用当前最新版本。
- backend 后台，使用vuejs技术栈，所有开发依赖包以vuejs的最新版为基准，全部使用当前最新版本。
- api 为前端和后台提供api接口，所有开发依赖包以fastapi的最新版为基准，全部使用当前最新版本。
各模块的文件夹结构均按照中等规模项目的最佳实践进行组织创建。

### 数据库
- host: localhost
- user: root
- password: cdc123321
- database: luyabaike
- tables:
  - company 品牌公司表
  - fish 鱼种表
  - fish_location 路亚基地表
  - reel 渔轮表
  - reel_item 渔轮型号表
  - rod 路亚竿表
  - rod_item 路亚竿型号表
  - series 品牌系列表
  - user 用户账号表
  - user_social 用户第三方账号表
对应的sql文件再 api/docs/luyabaike.sql中，所有models直接根据sql文件中的字段和表名生成，每个model一个文件。

### api接口
#### 模块说明
- 为前端和后台提供api接口。
#### 运行环境
- `python`版本使用最新的`3.13.5`，所有代码语法都基于`3.13.5`的最新语法。
- `fastapi`使用`0.116.1`版本
- `SQLModel`使用`0.0.24`版本
- `pydantic`使用`2.11.7`版本
- `pydantic-settings`使用`2.10.1`版本
- `uvicorn`使用`0.35.0`版本
- 使用`poetry`管理依赖包，所有开发依赖包以`fastapi`的最新版为基准，其他依赖包全部使用当前最新版本。
- 
#### 代码要求
- 从代码的可维护性、可扩展性、可测试性以及便于团队协作方面按照中等规模项目的最佳实践进行文件夹结构的组织创建。
- 如果需要使用.env，请直接创建。
- 配置合理的日志完善的组件。
- 项目结构参考 `api项目文件结构和说明.md`
#### 文件结构
- 加入`crud`目录: 仅进行数据库操作，不含业务逻辑。
- 加入`services`目录: 包含核心业务逻辑，协调 crud 层，是你的“大脑”。
- 加入`dependencies`: 存放 FastAPI 的依赖注入函数，如 get_db（获取数据库会话）、get_current_user（获取当前认证用户）等。
- 加入`utils`: 存放通用的辅助函数或工具类，这些函数不属于以上任何一层，但在项目中会多次使用（例如，邮件发送、文件上传处理等）。
- 将数据库相关代码拆分到`db`目录
  - `session.py` 配置 SQLModel 数据库引擎、会话本地化 (SessionLocal) 以及 FastAPI 中用于依赖注入的 get_db 函数。
  - `base.py` 定义 SQLModel ORM 模型的基础类，方便所有模型继承。
  - `init_db.py` 一个可选的脚本，用于在应用首次启动时初始化数据库，例如创建超级管理员用户或默认数据。



#### 主要实现接口：
- 路亚基地的查询
  - 搜索查询
  - 详情
- 路亚竿接口
  - 搜索查询
  - 详情
- 渔轮接口
  - 搜索查询
  - 详情