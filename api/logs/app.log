{"asctime": "2025-07-18 12:15:13,519", "name": "app.core.logging", "levelname": "INFO", "message": "\u65e5\u5fd7\u7cfb\u7edf\u5df2\u521d\u59cb\u5316\uff0c\u65e5\u5fd7\u7ea7\u522b: INFO", "taskName": null}
{"asctime": "2025-07-18 12:15:13,520", "name": "app.core.exceptions", "levelname": "INFO", "message": "\u5f02\u5e38\u5904\u7406\u5668\u5df2\u8bbe\u7f6e\u5b8c\u6210", "taskName": null}
{"asctime": "2025-07-18 12:15:13,520", "name": "app.core.events", "levelname": "INFO", "message": "\u5e94\u7528\u4e8b\u4ef6\u5904\u7406\u5668\u5df2\u8bbe\u7f6e\u5b8c\u6210", "taskName": null}
{"asctime": "2025-07-18 12:15:15,484", "name": "app.core.logging", "levelname": "INFO", "message": "\u65e5\u5fd7\u7cfb\u7edf\u5df2\u521d\u59cb\u5316\uff0c\u65e5\u5fd7\u7ea7\u522b: INFO", "taskName": null}
{"asctime": "2025-07-18 12:15:15,485", "name": "app.core.exceptions", "levelname": "INFO", "message": "\u5f02\u5e38\u5904\u7406\u5668\u5df2\u8bbe\u7f6e\u5b8c\u6210", "taskName": null}
{"asctime": "2025-07-18 12:15:15,485", "name": "app.core.events", "levelname": "INFO", "message": "\u5e94\u7528\u4e8b\u4ef6\u5904\u7406\u5668\u5df2\u8bbe\u7f6e\u5b8c\u6210", "taskName": null}
{"asctime": "2025-07-18 12:15:15,613", "name": "watchfiles.main", "levelname": "INFO", "message": "16 changes detected", "taskName": null}
{"asctime": "2025-07-18 12:15:15,979", "name": "watchfiles.main", "levelname": "INFO", "message": "28 changes detected", "taskName": null}
{"asctime": "2025-07-18 12:15:16,340", "name": "watchfiles.main", "levelname": "INFO", "message": "3 changes detected", "taskName": null}
{"asctime": "2025-07-18 12:15:16,701", "name": "watchfiles.main", "levelname": "INFO", "message": "48 changes detected", "taskName": null}
{"asctime": "2025-07-18 12:15:16,907", "name": "app.core.logging", "levelname": "INFO", "message": "\u65e5\u5fd7\u7cfb\u7edf\u5df2\u521d\u59cb\u5316\uff0c\u65e5\u5fd7\u7ea7\u522b: INFO", "taskName": "Task-1"}
{"asctime": "2025-07-18 12:15:16,907", "name": "app.core.exceptions", "levelname": "INFO", "message": "\u5f02\u5e38\u5904\u7406\u5668\u5df2\u8bbe\u7f6e\u5b8c\u6210", "taskName": "Task-1"}
{"asctime": "2025-07-18 12:15:16,907", "name": "app.core.events", "levelname": "INFO", "message": "\u5e94\u7528\u4e8b\u4ef6\u5904\u7406\u5668\u5df2\u8bbe\u7f6e\u5b8c\u6210", "taskName": "Task-1"}
{"asctime": "2025-07-18 12:15:16,920", "name": "app.core.events", "levelname": "INFO", "message": "\u5e94\u7528\u542f\u52a8\u4e2d...", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:15:16,920", "name": "app.db.init_db", "levelname": "INFO", "message": "\u5f00\u59cb\u521d\u59cb\u5316\u6570\u636e\u5e93...", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:15:17,063", "name": "watchfiles.main", "levelname": "INFO", "message": "63 changes detected", "taskName": null}
{"asctime": "2025-07-18 12:15:17,075", "name": "app.db.init_db", "levelname": "INFO", "message": "\u6570\u636e\u5e93\u8868\u521b\u5efa\u6210\u529f", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:15:17,075", "name": "app.db.init_db", "levelname": "INFO", "message": "\u6570\u636e\u5e93\u521d\u59cb\u5316\u5b8c\u6210", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:15:17,075", "name": "app.core.events", "levelname": "INFO", "message": "\u6570\u636e\u5e93\u521d\u59cb\u5316\u5b8c\u6210", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:15:17,075", "name": "app.core.events", "levelname": "INFO", "message": "\u5e94\u7528\u542f\u52a8\u5b8c\u6210", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:16:56,773", "name": "app.core.events", "levelname": "INFO", "message": "\u5e94\u7528\u5173\u95ed\u4e2d...", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:16:56,773", "name": "app.core.events", "levelname": "INFO", "message": "\u5e94\u7528\u5df2\u5173\u95ed", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:17:23,194", "name": "app.core.logging", "levelname": "INFO", "message": "\u65e5\u5fd7\u7cfb\u7edf\u5df2\u521d\u59cb\u5316\uff0c\u65e5\u5fd7\u7ea7\u522b: INFO", "taskName": "Task-1"}
{"asctime": "2025-07-18 12:17:23,195", "name": "app.core.exceptions", "levelname": "INFO", "message": "\u5f02\u5e38\u5904\u7406\u5668\u5df2\u8bbe\u7f6e\u5b8c\u6210", "taskName": "Task-1"}
{"asctime": "2025-07-18 12:17:23,196", "name": "app.core.events", "levelname": "INFO", "message": "\u5e94\u7528\u4e8b\u4ef6\u5904\u7406\u5668\u5df2\u8bbe\u7f6e\u5b8c\u6210", "taskName": "Task-1"}
{"asctime": "2025-07-18 12:17:23,210", "name": "app.core.events", "levelname": "INFO", "message": "\u5e94\u7528\u542f\u52a8\u4e2d...", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:17:23,210", "name": "app.db.init_db", "levelname": "INFO", "message": "\u5f00\u59cb\u521d\u59cb\u5316\u6570\u636e\u5e93...", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:17:23,244", "name": "app.db.init_db", "levelname": "INFO", "message": "\u6570\u636e\u5e93\u8868\u521b\u5efa\u6210\u529f", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:17:23,245", "name": "app.db.init_db", "levelname": "INFO", "message": "\u6570\u636e\u5e93\u521d\u59cb\u5316\u5b8c\u6210", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:17:23,245", "name": "app.core.events", "levelname": "INFO", "message": "\u6570\u636e\u5e93\u521d\u59cb\u5316\u5b8c\u6210", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:17:23,245", "name": "app.core.events", "levelname": "INFO", "message": "\u5e94\u7528\u542f\u52a8\u5b8c\u6210", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:17:30,528", "name": "app.core.exceptions", "levelname": "WARNING", "message": "HTTP Exception: Not Found", "taskName": "Task-3"}
{"asctime": "2025-07-18 12:17:32,818", "name": "app.core.exceptions", "levelname": "WARNING", "message": "HTTP Exception: Not Found", "taskName": "Task-4"}
{"asctime": "2025-07-18 12:17:48,470", "name": "app.core.exceptions", "levelname": "WARNING", "message": "HTTP Exception: Not Found", "taskName": "Task-6"}
{"asctime": "2025-07-18 12:17:51,373", "name": "app.core.exceptions", "levelname": "WARNING", "message": "HTTP Exception: Not Found", "taskName": "Task-7"}
{"asctime": "2025-07-18 12:18:14,399", "name": "app.core.logging", "levelname": "INFO", "message": "\u65e5\u5fd7\u7cfb\u7edf\u5df2\u521d\u59cb\u5316\uff0c\u65e5\u5fd7\u7ea7\u522b: INFO", "taskName": "Task-1"}
{"asctime": "2025-07-18 12:18:14,400", "name": "app.core.exceptions", "levelname": "INFO", "message": "\u5f02\u5e38\u5904\u7406\u5668\u5df2\u8bbe\u7f6e\u5b8c\u6210", "taskName": "Task-1"}
{"asctime": "2025-07-18 12:18:14,400", "name": "app.core.events", "levelname": "INFO", "message": "\u5e94\u7528\u4e8b\u4ef6\u5904\u7406\u5668\u5df2\u8bbe\u7f6e\u5b8c\u6210", "taskName": "Task-1"}
{"asctime": "2025-07-18 12:18:14,414", "name": "app.core.events", "levelname": "INFO", "message": "\u5e94\u7528\u542f\u52a8\u4e2d...", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:18:14,414", "name": "app.db.init_db", "levelname": "INFO", "message": "\u5f00\u59cb\u521d\u59cb\u5316\u6570\u636e\u5e93...", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:18:14,444", "name": "app.db.init_db", "levelname": "INFO", "message": "\u6570\u636e\u5e93\u8868\u521b\u5efa\u6210\u529f", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:18:14,444", "name": "app.db.init_db", "levelname": "INFO", "message": "\u6570\u636e\u5e93\u521d\u59cb\u5316\u5b8c\u6210", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:18:14,444", "name": "app.core.events", "levelname": "INFO", "message": "\u6570\u636e\u5e93\u521d\u59cb\u5316\u5b8c\u6210", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:18:14,444", "name": "app.core.events", "levelname": "INFO", "message": "\u5e94\u7528\u542f\u52a8\u5b8c\u6210", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:18:44,910", "name": "app.core.exceptions", "levelname": "ERROR", "message": "Unexpected Error: 'generator' object has no attribute 'exec'", "exc_info": "Traceback (most recent call last):\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py\", line 164, in __call__\n    await self.app(scope, receive, _send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py\", line 85, in __call__\n    await self.app(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/exceptions.py\", line 63, in __call__\n    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 53, in wrapped_app\n    raise exc\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 42, in wrapped_app\n    await app(scope, receive, sender)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 716, in __call__\n    await self.middleware_stack(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 736, in app\n    await route.handle(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 290, in handle\n    await self.app(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 78, in app\n    await wrap_app_handling_exceptions(app, request)(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 53, in wrapped_app\n    raise exc\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 42, in wrapped_app\n    await app(scope, receive, sender)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 75, in app\n    response = await f(request)\n               ^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/fastapi/routing.py\", line 302, in app\n    raw_response = await run_endpoint_function(\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    ...<3 lines>...\n    )\n    ^\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/fastapi/routing.py\", line 213, in run_endpoint_function\n    return await dependant.call(**values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/app/api/v1/endpoints/fishing_locations.py\", line 55, in search_fishing_locations\n    locations, pagination = fishing_location_service.search_fishing_locations(db, search_params)\n                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/app/services/fishing_location_service.py\", line 39, in search_fishing_locations\n    locations = fishing_location.search(\n        db,\n    ...<7 lines>...\n        limit=search_params.page_size\n    )\n  File \"/Users/<USER>/cdc/luyabaike/api/app/crud/fishing_location.py\", line 59, in search\n    return db.exec(statement).all()\n           ^^^^^^^\nAttributeError: 'generator' object has no attribute 'exec'", "taskName": "Task-5"}
{"asctime": "2025-07-18 12:18:46,104", "name": "app.core.exceptions", "levelname": "ERROR", "message": "Unexpected Error: 'generator' object has no attribute 'exec'", "exc_info": "Traceback (most recent call last):\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py\", line 164, in __call__\n    await self.app(scope, receive, _send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py\", line 85, in __call__\n    await self.app(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/exceptions.py\", line 63, in __call__\n    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 53, in wrapped_app\n    raise exc\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 42, in wrapped_app\n    await app(scope, receive, sender)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 716, in __call__\n    await self.middleware_stack(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 736, in app\n    await route.handle(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 290, in handle\n    await self.app(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 78, in app\n    await wrap_app_handling_exceptions(app, request)(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 53, in wrapped_app\n    raise exc\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 42, in wrapped_app\n    await app(scope, receive, sender)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 75, in app\n    response = await f(request)\n               ^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/fastapi/routing.py\", line 302, in app\n    raw_response = await run_endpoint_function(\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    ...<3 lines>...\n    )\n    ^\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/fastapi/routing.py\", line 213, in run_endpoint_function\n    return await dependant.call(**values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/app/api/v1/endpoints/fishing_locations.py\", line 55, in search_fishing_locations\n    locations, pagination = fishing_location_service.search_fishing_locations(db, search_params)\n                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/app/services/fishing_location_service.py\", line 39, in search_fishing_locations\n    locations = fishing_location.search(\n        db,\n    ...<7 lines>...\n        limit=search_params.page_size\n    )\n  File \"/Users/<USER>/cdc/luyabaike/api/app/crud/fishing_location.py\", line 59, in search\n    return db.exec(statement).all()\n           ^^^^^^^\nAttributeError: 'generator' object has no attribute 'exec'", "taskName": "Task-6"}
{"asctime": "2025-07-18 12:20:12,909", "name": "app.core.exceptions", "levelname": "ERROR", "message": "Unexpected Error: 'generator' object has no attribute 'exec'", "exc_info": "Traceback (most recent call last):\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py\", line 164, in __call__\n    await self.app(scope, receive, _send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py\", line 85, in __call__\n    await self.app(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/exceptions.py\", line 63, in __call__\n    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 53, in wrapped_app\n    raise exc\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 42, in wrapped_app\n    await app(scope, receive, sender)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 716, in __call__\n    await self.middleware_stack(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 736, in app\n    await route.handle(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 290, in handle\n    await self.app(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 78, in app\n    await wrap_app_handling_exceptions(app, request)(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 53, in wrapped_app\n    raise exc\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 42, in wrapped_app\n    await app(scope, receive, sender)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 75, in app\n    response = await f(request)\n               ^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/fastapi/routing.py\", line 302, in app\n    raw_response = await run_endpoint_function(\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    ...<3 lines>...\n    )\n    ^\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/fastapi/routing.py\", line 213, in run_endpoint_function\n    return await dependant.call(**values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/app/api/v1/endpoints/fishing_locations.py\", line 55, in search_fishing_locations\n    locations, pagination = fishing_location_service.search_fishing_locations(db, search_params)\n                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/app/services/fishing_location_service.py\", line 39, in search_fishing_locations\n    locations = fishing_location.search(\n        db,\n    ...<7 lines>...\n        limit=search_params.page_size\n    )\n  File \"/Users/<USER>/cdc/luyabaike/api/app/crud/fishing_location.py\", line 59, in search\n    return db.exec(statement).all()\n           ^^^^^^^\nAttributeError: 'generator' object has no attribute 'exec'", "taskName": "Task-7"}
{"asctime": "2025-07-18 12:20:55,843", "name": "app.core.logging", "levelname": "INFO", "message": "\u65e5\u5fd7\u7cfb\u7edf\u5df2\u521d\u59cb\u5316\uff0c\u65e5\u5fd7\u7ea7\u522b: INFO", "taskName": "Task-1"}
{"asctime": "2025-07-18 12:20:55,844", "name": "app.core.exceptions", "levelname": "INFO", "message": "\u5f02\u5e38\u5904\u7406\u5668\u5df2\u8bbe\u7f6e\u5b8c\u6210", "taskName": "Task-1"}
{"asctime": "2025-07-18 12:20:55,844", "name": "app.core.events", "levelname": "INFO", "message": "\u5e94\u7528\u4e8b\u4ef6\u5904\u7406\u5668\u5df2\u8bbe\u7f6e\u5b8c\u6210", "taskName": "Task-1"}
{"asctime": "2025-07-18 12:20:55,856", "name": "app.core.events", "levelname": "INFO", "message": "\u5e94\u7528\u542f\u52a8\u4e2d...", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:20:55,856", "name": "app.db.init_db", "levelname": "INFO", "message": "\u5f00\u59cb\u521d\u59cb\u5316\u6570\u636e\u5e93...", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:20:55,885", "name": "app.db.init_db", "levelname": "INFO", "message": "\u6570\u636e\u5e93\u8868\u521b\u5efa\u6210\u529f", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:20:55,885", "name": "app.db.init_db", "levelname": "INFO", "message": "\u6570\u636e\u5e93\u521d\u59cb\u5316\u5b8c\u6210", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:20:55,885", "name": "app.core.events", "levelname": "INFO", "message": "\u6570\u636e\u5e93\u521d\u59cb\u5316\u5b8c\u6210", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:20:55,886", "name": "app.core.events", "levelname": "INFO", "message": "\u5e94\u7528\u542f\u52a8\u5b8c\u6210", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:21:32,793", "name": "app.core.logging", "levelname": "INFO", "message": "\u65e5\u5fd7\u7cfb\u7edf\u5df2\u521d\u59cb\u5316\uff0c\u65e5\u5fd7\u7ea7\u522b: INFO", "taskName": null}
{"asctime": "2025-07-18 12:21:32,794", "name": "app.core.exceptions", "levelname": "INFO", "message": "\u5f02\u5e38\u5904\u7406\u5668\u5df2\u8bbe\u7f6e\u5b8c\u6210", "taskName": null}
{"asctime": "2025-07-18 12:21:32,794", "name": "app.core.events", "levelname": "INFO", "message": "\u5e94\u7528\u4e8b\u4ef6\u5904\u7406\u5668\u5df2\u8bbe\u7f6e\u5b8c\u6210", "taskName": null}
{"asctime": "2025-07-18 12:24:37,461", "name": "app.core.logging", "levelname": "INFO", "message": "\u65e5\u5fd7\u7cfb\u7edf\u5df2\u521d\u59cb\u5316\uff0c\u65e5\u5fd7\u7ea7\u522b: INFO", "taskName": null}
{"asctime": "2025-07-18 12:24:37,461", "name": "app.core.exceptions", "levelname": "INFO", "message": "\u5f02\u5e38\u5904\u7406\u5668\u5df2\u8bbe\u7f6e\u5b8c\u6210", "taskName": null}
{"asctime": "2025-07-18 12:24:37,462", "name": "app.core.events", "levelname": "INFO", "message": "\u5e94\u7528\u4e8b\u4ef6\u5904\u7406\u5668\u5df2\u8bbe\u7f6e\u5b8c\u6210", "taskName": null}
{"asctime": "2025-07-18 12:24:38,779", "name": "app.core.logging", "levelname": "INFO", "message": "\u65e5\u5fd7\u7cfb\u7edf\u5df2\u521d\u59cb\u5316\uff0c\u65e5\u5fd7\u7ea7\u522b: INFO", "taskName": null}
{"asctime": "2025-07-18 12:24:38,781", "name": "app.core.exceptions", "levelname": "INFO", "message": "\u5f02\u5e38\u5904\u7406\u5668\u5df2\u8bbe\u7f6e\u5b8c\u6210", "taskName": null}
{"asctime": "2025-07-18 12:24:38,781", "name": "app.core.events", "levelname": "INFO", "message": "\u5e94\u7528\u4e8b\u4ef6\u5904\u7406\u5668\u5df2\u8bbe\u7f6e\u5b8c\u6210", "taskName": null}
{"asctime": "2025-07-18 12:24:38,941", "name": "watchfiles.main", "levelname": "INFO", "message": "1 change detected", "taskName": null}
{"asctime": "2025-07-18 12:24:38,961", "name": "app.core.logging", "levelname": "INFO", "message": "\u65e5\u5fd7\u7cfb\u7edf\u5df2\u521d\u59cb\u5316\uff0c\u65e5\u5fd7\u7ea7\u522b: INFO", "taskName": "Task-1"}
{"asctime": "2025-07-18 12:24:38,962", "name": "app.core.exceptions", "levelname": "INFO", "message": "\u5f02\u5e38\u5904\u7406\u5668\u5df2\u8bbe\u7f6e\u5b8c\u6210", "taskName": "Task-1"}
{"asctime": "2025-07-18 12:24:38,962", "name": "app.core.events", "levelname": "INFO", "message": "\u5e94\u7528\u4e8b\u4ef6\u5904\u7406\u5668\u5df2\u8bbe\u7f6e\u5b8c\u6210", "taskName": "Task-1"}
{"asctime": "2025-07-18 12:24:38,977", "name": "app.core.events", "levelname": "INFO", "message": "\u5e94\u7528\u542f\u52a8\u4e2d...", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:24:38,977", "name": "app.db.init_db", "levelname": "INFO", "message": "\u5f00\u59cb\u521d\u59cb\u5316\u6570\u636e\u5e93...", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:24:39,011", "name": "app.db.init_db", "levelname": "INFO", "message": "\u6570\u636e\u5e93\u8868\u521b\u5efa\u6210\u529f", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:24:39,011", "name": "app.db.init_db", "levelname": "INFO", "message": "\u6570\u636e\u5e93\u521d\u59cb\u5316\u5b8c\u6210", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:24:39,011", "name": "app.core.events", "levelname": "INFO", "message": "\u6570\u636e\u5e93\u521d\u59cb\u5316\u5b8c\u6210", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:24:39,011", "name": "app.core.events", "levelname": "INFO", "message": "\u5e94\u7528\u542f\u52a8\u5b8c\u6210", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:28:24,061", "name": "app.core.exceptions", "levelname": "ERROR", "message": "Unexpected Error: 'generator' object has no attribute 'exec'", "exc_info": "Traceback (most recent call last):\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py\", line 164, in __call__\n    await self.app(scope, receive, _send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py\", line 85, in __call__\n    await self.app(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/exceptions.py\", line 63, in __call__\n    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 53, in wrapped_app\n    raise exc\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 42, in wrapped_app\n    await app(scope, receive, sender)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 716, in __call__\n    await self.middleware_stack(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 736, in app\n    await route.handle(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 290, in handle\n    await self.app(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 78, in app\n    await wrap_app_handling_exceptions(app, request)(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 53, in wrapped_app\n    raise exc\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 42, in wrapped_app\n    await app(scope, receive, sender)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 75, in app\n    response = await f(request)\n               ^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/fastapi/routing.py\", line 302, in app\n    raw_response = await run_endpoint_function(\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    ...<3 lines>...\n    )\n    ^\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/fastapi/routing.py\", line 213, in run_endpoint_function\n    return await dependant.call(**values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/app/api/v1/endpoints/fishing_locations.py\", line 55, in search_fishing_locations\n    locations, pagination = fishing_location_service.search_fishing_locations(db, search_params)\n                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/app/services/fishing_location_service.py\", line 39, in search_fishing_locations\n    locations = fishing_location.search(\n        db,\n    ...<7 lines>...\n        limit=search_params.page_size\n    )\n  File \"/Users/<USER>/cdc/luyabaike/api/app/crud/fishing_location.py\", line 59, in search\n    return db.exec(statement).all()\n           ^^^^^^^\nAttributeError: 'generator' object has no attribute 'exec'", "taskName": "Task-3"}
{"asctime": "2025-07-18 12:36:10,371", "name": "app.core.logging", "levelname": "INFO", "message": "\u65e5\u5fd7\u7cfb\u7edf\u5df2\u521d\u59cb\u5316\uff0c\u65e5\u5fd7\u7ea7\u522b: INFO", "taskName": null}
{"asctime": "2025-07-18 12:36:10,372", "name": "app.core.exceptions", "levelname": "INFO", "message": "\u5f02\u5e38\u5904\u7406\u5668\u5df2\u8bbe\u7f6e\u5b8c\u6210", "taskName": null}
{"asctime": "2025-07-18 12:36:10,372", "name": "app.core.events", "levelname": "INFO", "message": "\u5e94\u7528\u4e8b\u4ef6\u5904\u7406\u5668\u5df2\u8bbe\u7f6e\u5b8c\u6210", "taskName": null}
{"asctime": "2025-07-18 12:36:11,601", "name": "app.core.logging", "levelname": "INFO", "message": "\u65e5\u5fd7\u7cfb\u7edf\u5df2\u521d\u59cb\u5316\uff0c\u65e5\u5fd7\u7ea7\u522b: INFO", "taskName": null}
{"asctime": "2025-07-18 12:36:11,602", "name": "app.core.exceptions", "levelname": "INFO", "message": "\u5f02\u5e38\u5904\u7406\u5668\u5df2\u8bbe\u7f6e\u5b8c\u6210", "taskName": null}
{"asctime": "2025-07-18 12:36:11,602", "name": "app.core.events", "levelname": "INFO", "message": "\u5e94\u7528\u4e8b\u4ef6\u5904\u7406\u5668\u5df2\u8bbe\u7f6e\u5b8c\u6210", "taskName": null}
{"asctime": "2025-07-18 12:36:11,721", "name": "app.core.logging", "levelname": "INFO", "message": "\u65e5\u5fd7\u7cfb\u7edf\u5df2\u521d\u59cb\u5316\uff0c\u65e5\u5fd7\u7ea7\u522b: INFO", "taskName": "Task-1"}
{"asctime": "2025-07-18 12:36:11,722", "name": "app.core.exceptions", "levelname": "INFO", "message": "\u5f02\u5e38\u5904\u7406\u5668\u5df2\u8bbe\u7f6e\u5b8c\u6210", "taskName": "Task-1"}
{"asctime": "2025-07-18 12:36:11,722", "name": "app.core.events", "levelname": "INFO", "message": "\u5e94\u7528\u4e8b\u4ef6\u5904\u7406\u5668\u5df2\u8bbe\u7f6e\u5b8c\u6210", "taskName": "Task-1"}
{"asctime": "2025-07-18 12:36:11,737", "name": "app.core.events", "levelname": "INFO", "message": "\u5e94\u7528\u542f\u52a8\u4e2d...", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:36:11,737", "name": "watchfiles.main", "levelname": "INFO", "message": "1 change detected", "taskName": null}
{"asctime": "2025-07-18 12:36:11,737", "name": "app.db.init_db", "levelname": "INFO", "message": "\u5f00\u59cb\u521d\u59cb\u5316\u6570\u636e\u5e93...", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:36:11,772", "name": "app.db.init_db", "levelname": "INFO", "message": "\u6570\u636e\u5e93\u8868\u521b\u5efa\u6210\u529f", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:36:11,772", "name": "app.db.init_db", "levelname": "INFO", "message": "\u6570\u636e\u5e93\u521d\u59cb\u5316\u5b8c\u6210", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:36:11,772", "name": "app.core.events", "levelname": "INFO", "message": "\u6570\u636e\u5e93\u521d\u59cb\u5316\u5b8c\u6210", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:36:11,772", "name": "app.core.events", "levelname": "INFO", "message": "\u5e94\u7528\u542f\u52a8\u5b8c\u6210", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:36:59,050", "name": "app.core.logging", "levelname": "INFO", "message": "\u65e5\u5fd7\u7cfb\u7edf\u5df2\u521d\u59cb\u5316\uff0c\u65e5\u5fd7\u7ea7\u522b: INFO", "taskName": "Task-1"}
{"asctime": "2025-07-18 12:36:59,050", "name": "app.core.exceptions", "levelname": "INFO", "message": "\u5f02\u5e38\u5904\u7406\u5668\u5df2\u8bbe\u7f6e\u5b8c\u6210", "taskName": "Task-1"}
{"asctime": "2025-07-18 12:36:59,051", "name": "app.core.events", "levelname": "INFO", "message": "\u5e94\u7528\u4e8b\u4ef6\u5904\u7406\u5668\u5df2\u8bbe\u7f6e\u5b8c\u6210", "taskName": "Task-1"}
{"asctime": "2025-07-18 12:36:59,065", "name": "app.core.events", "levelname": "INFO", "message": "\u5e94\u7528\u542f\u52a8\u4e2d...", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:36:59,065", "name": "app.db.init_db", "levelname": "INFO", "message": "\u5f00\u59cb\u521d\u59cb\u5316\u6570\u636e\u5e93...", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:36:59,097", "name": "app.db.init_db", "levelname": "INFO", "message": "\u6570\u636e\u5e93\u8868\u521b\u5efa\u6210\u529f", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:36:59,097", "name": "app.db.init_db", "levelname": "INFO", "message": "\u6570\u636e\u5e93\u521d\u59cb\u5316\u5b8c\u6210", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:36:59,097", "name": "app.core.events", "levelname": "INFO", "message": "\u6570\u636e\u5e93\u521d\u59cb\u5316\u5b8c\u6210", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:36:59,097", "name": "app.core.events", "levelname": "INFO", "message": "\u5e94\u7528\u542f\u52a8\u5b8c\u6210", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:37:33,188", "name": "app.core.events", "levelname": "INFO", "message": "\u5e94\u7528\u5173\u95ed\u4e2d...", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:37:33,188", "name": "app.core.events", "levelname": "INFO", "message": "\u5e94\u7528\u5df2\u5173\u95ed", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:37:34,808", "name": "app.core.logging", "levelname": "INFO", "message": "\u65e5\u5fd7\u7cfb\u7edf\u5df2\u521d\u59cb\u5316\uff0c\u65e5\u5fd7\u7ea7\u522b: INFO", "taskName": "Task-1"}
{"asctime": "2025-07-18 12:37:34,809", "name": "app.core.exceptions", "levelname": "INFO", "message": "\u5f02\u5e38\u5904\u7406\u5668\u5df2\u8bbe\u7f6e\u5b8c\u6210", "taskName": "Task-1"}
{"asctime": "2025-07-18 12:37:34,809", "name": "app.core.events", "levelname": "INFO", "message": "\u5e94\u7528\u4e8b\u4ef6\u5904\u7406\u5668\u5df2\u8bbe\u7f6e\u5b8c\u6210", "taskName": "Task-1"}
{"asctime": "2025-07-18 12:37:34,825", "name": "app.core.events", "levelname": "INFO", "message": "\u5e94\u7528\u542f\u52a8\u4e2d...", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:37:34,826", "name": "app.core.events", "levelname": "INFO", "message": "\u6570\u636e\u5e93\u521d\u59cb\u5316\u8df3\u8fc7", "taskName": "Task-2"}
{"asctime": "2025-07-18 12:37:34,826", "name": "app.core.events", "levelname": "INFO", "message": "\u5e94\u7528\u542f\u52a8\u5b8c\u6210", "taskName": "Task-2"}
