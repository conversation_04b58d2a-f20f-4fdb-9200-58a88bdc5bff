{"asctime": "2025-07-18 12:18:44,910", "name": "app.core.exceptions", "levelname": "ERROR", "message": "Unexpected Error: 'generator' object has no attribute 'exec'", "exc_info": "Traceback (most recent call last):\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py\", line 164, in __call__\n    await self.app(scope, receive, _send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py\", line 85, in __call__\n    await self.app(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/exceptions.py\", line 63, in __call__\n    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 53, in wrapped_app\n    raise exc\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 42, in wrapped_app\n    await app(scope, receive, sender)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 716, in __call__\n    await self.middleware_stack(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 736, in app\n    await route.handle(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 290, in handle\n    await self.app(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 78, in app\n    await wrap_app_handling_exceptions(app, request)(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 53, in wrapped_app\n    raise exc\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 42, in wrapped_app\n    await app(scope, receive, sender)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 75, in app\n    response = await f(request)\n               ^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/fastapi/routing.py\", line 302, in app\n    raw_response = await run_endpoint_function(\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    ...<3 lines>...\n    )\n    ^\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/fastapi/routing.py\", line 213, in run_endpoint_function\n    return await dependant.call(**values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/app/api/v1/endpoints/fishing_locations.py\", line 55, in search_fishing_locations\n    locations, pagination = fishing_location_service.search_fishing_locations(db, search_params)\n                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/app/services/fishing_location_service.py\", line 39, in search_fishing_locations\n    locations = fishing_location.search(\n        db,\n    ...<7 lines>...\n        limit=search_params.page_size\n    )\n  File \"/Users/<USER>/cdc/luyabaike/api/app/crud/fishing_location.py\", line 59, in search\n    return db.exec(statement).all()\n           ^^^^^^^\nAttributeError: 'generator' object has no attribute 'exec'", "taskName": "Task-5"}
{"asctime": "2025-07-18 12:18:44,933", "name": "uvicorn.error", "levelname": "ERROR", "message": "Exception in ASGI application\n", "exc_info": "Traceback (most recent call last):\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/uvicorn/protocols/http/httptools_impl.py\", line 409, in run_asgi\n    result = await app(  # type: ignore[func-returns-value]\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n        self.scope, self.receive, self.send\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/uvicorn/middleware/proxy_headers.py\", line 60, in __call__\n    return await self.app(scope, receive, send)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/fastapi/applications.py\", line 1054, in __call__\n    await super().__call__(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/applications.py\", line 113, in __call__\n    await self.middleware_stack(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py\", line 186, in __call__\n    raise exc\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py\", line 164, in __call__\n    await self.app(scope, receive, _send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py\", line 85, in __call__\n    await self.app(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/exceptions.py\", line 63, in __call__\n    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 53, in wrapped_app\n    raise exc\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 42, in wrapped_app\n    await app(scope, receive, sender)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 716, in __call__\n    await self.middleware_stack(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 736, in app\n    await route.handle(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 290, in handle\n    await self.app(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 78, in app\n    await wrap_app_handling_exceptions(app, request)(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 53, in wrapped_app\n    raise exc\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 42, in wrapped_app\n    await app(scope, receive, sender)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 75, in app\n    response = await f(request)\n               ^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/fastapi/routing.py\", line 302, in app\n    raw_response = await run_endpoint_function(\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    ...<3 lines>...\n    )\n    ^\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/fastapi/routing.py\", line 213, in run_endpoint_function\n    return await dependant.call(**values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/app/api/v1/endpoints/fishing_locations.py\", line 55, in search_fishing_locations\n    locations, pagination = fishing_location_service.search_fishing_locations(db, search_params)\n                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/app/services/fishing_location_service.py\", line 39, in search_fishing_locations\n    locations = fishing_location.search(\n        db,\n    ...<7 lines>...\n        limit=search_params.page_size\n    )\n  File \"/Users/<USER>/cdc/luyabaike/api/app/crud/fishing_location.py\", line 59, in search\n    return db.exec(statement).all()\n           ^^^^^^^\nAttributeError: 'generator' object has no attribute 'exec'", "taskName": "Task-5"}
{"asctime": "2025-07-18 12:18:46,104", "name": "app.core.exceptions", "levelname": "ERROR", "message": "Unexpected Error: 'generator' object has no attribute 'exec'", "exc_info": "Traceback (most recent call last):\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py\", line 164, in __call__\n    await self.app(scope, receive, _send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py\", line 85, in __call__\n    await self.app(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/exceptions.py\", line 63, in __call__\n    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 53, in wrapped_app\n    raise exc\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 42, in wrapped_app\n    await app(scope, receive, sender)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 716, in __call__\n    await self.middleware_stack(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 736, in app\n    await route.handle(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 290, in handle\n    await self.app(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 78, in app\n    await wrap_app_handling_exceptions(app, request)(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 53, in wrapped_app\n    raise exc\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 42, in wrapped_app\n    await app(scope, receive, sender)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 75, in app\n    response = await f(request)\n               ^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/fastapi/routing.py\", line 302, in app\n    raw_response = await run_endpoint_function(\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    ...<3 lines>...\n    )\n    ^\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/fastapi/routing.py\", line 213, in run_endpoint_function\n    return await dependant.call(**values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/app/api/v1/endpoints/fishing_locations.py\", line 55, in search_fishing_locations\n    locations, pagination = fishing_location_service.search_fishing_locations(db, search_params)\n                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/app/services/fishing_location_service.py\", line 39, in search_fishing_locations\n    locations = fishing_location.search(\n        db,\n    ...<7 lines>...\n        limit=search_params.page_size\n    )\n  File \"/Users/<USER>/cdc/luyabaike/api/app/crud/fishing_location.py\", line 59, in search\n    return db.exec(statement).all()\n           ^^^^^^^\nAttributeError: 'generator' object has no attribute 'exec'", "taskName": "Task-6"}
{"asctime": "2025-07-18 12:18:46,119", "name": "uvicorn.error", "levelname": "ERROR", "message": "Exception in ASGI application\n", "exc_info": "Traceback (most recent call last):\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/uvicorn/protocols/http/httptools_impl.py\", line 409, in run_asgi\n    result = await app(  # type: ignore[func-returns-value]\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n        self.scope, self.receive, self.send\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/uvicorn/middleware/proxy_headers.py\", line 60, in __call__\n    return await self.app(scope, receive, send)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/fastapi/applications.py\", line 1054, in __call__\n    await super().__call__(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/applications.py\", line 113, in __call__\n    await self.middleware_stack(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py\", line 186, in __call__\n    raise exc\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py\", line 164, in __call__\n    await self.app(scope, receive, _send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py\", line 85, in __call__\n    await self.app(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/exceptions.py\", line 63, in __call__\n    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 53, in wrapped_app\n    raise exc\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 42, in wrapped_app\n    await app(scope, receive, sender)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 716, in __call__\n    await self.middleware_stack(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 736, in app\n    await route.handle(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 290, in handle\n    await self.app(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 78, in app\n    await wrap_app_handling_exceptions(app, request)(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 53, in wrapped_app\n    raise exc\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 42, in wrapped_app\n    await app(scope, receive, sender)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 75, in app\n    response = await f(request)\n               ^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/fastapi/routing.py\", line 302, in app\n    raw_response = await run_endpoint_function(\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    ...<3 lines>...\n    )\n    ^\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/fastapi/routing.py\", line 213, in run_endpoint_function\n    return await dependant.call(**values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/app/api/v1/endpoints/fishing_locations.py\", line 55, in search_fishing_locations\n    locations, pagination = fishing_location_service.search_fishing_locations(db, search_params)\n                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/app/services/fishing_location_service.py\", line 39, in search_fishing_locations\n    locations = fishing_location.search(\n        db,\n    ...<7 lines>...\n        limit=search_params.page_size\n    )\n  File \"/Users/<USER>/cdc/luyabaike/api/app/crud/fishing_location.py\", line 59, in search\n    return db.exec(statement).all()\n           ^^^^^^^\nAttributeError: 'generator' object has no attribute 'exec'", "taskName": "Task-6"}
{"asctime": "2025-07-18 12:20:12,909", "name": "app.core.exceptions", "levelname": "ERROR", "message": "Unexpected Error: 'generator' object has no attribute 'exec'", "exc_info": "Traceback (most recent call last):\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py\", line 164, in __call__\n    await self.app(scope, receive, _send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py\", line 85, in __call__\n    await self.app(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/exceptions.py\", line 63, in __call__\n    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 53, in wrapped_app\n    raise exc\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 42, in wrapped_app\n    await app(scope, receive, sender)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 716, in __call__\n    await self.middleware_stack(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 736, in app\n    await route.handle(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 290, in handle\n    await self.app(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 78, in app\n    await wrap_app_handling_exceptions(app, request)(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 53, in wrapped_app\n    raise exc\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 42, in wrapped_app\n    await app(scope, receive, sender)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 75, in app\n    response = await f(request)\n               ^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/fastapi/routing.py\", line 302, in app\n    raw_response = await run_endpoint_function(\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    ...<3 lines>...\n    )\n    ^\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/fastapi/routing.py\", line 213, in run_endpoint_function\n    return await dependant.call(**values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/app/api/v1/endpoints/fishing_locations.py\", line 55, in search_fishing_locations\n    locations, pagination = fishing_location_service.search_fishing_locations(db, search_params)\n                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/app/services/fishing_location_service.py\", line 39, in search_fishing_locations\n    locations = fishing_location.search(\n        db,\n    ...<7 lines>...\n        limit=search_params.page_size\n    )\n  File \"/Users/<USER>/cdc/luyabaike/api/app/crud/fishing_location.py\", line 59, in search\n    return db.exec(statement).all()\n           ^^^^^^^\nAttributeError: 'generator' object has no attribute 'exec'", "taskName": "Task-7"}
{"asctime": "2025-07-18 12:20:12,923", "name": "uvicorn.error", "levelname": "ERROR", "message": "Exception in ASGI application\n", "exc_info": "Traceback (most recent call last):\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/uvicorn/protocols/http/httptools_impl.py\", line 409, in run_asgi\n    result = await app(  # type: ignore[func-returns-value]\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n        self.scope, self.receive, self.send\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/uvicorn/middleware/proxy_headers.py\", line 60, in __call__\n    return await self.app(scope, receive, send)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/fastapi/applications.py\", line 1054, in __call__\n    await super().__call__(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/applications.py\", line 113, in __call__\n    await self.middleware_stack(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py\", line 186, in __call__\n    raise exc\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py\", line 164, in __call__\n    await self.app(scope, receive, _send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py\", line 85, in __call__\n    await self.app(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/exceptions.py\", line 63, in __call__\n    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 53, in wrapped_app\n    raise exc\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 42, in wrapped_app\n    await app(scope, receive, sender)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 716, in __call__\n    await self.middleware_stack(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 736, in app\n    await route.handle(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 290, in handle\n    await self.app(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 78, in app\n    await wrap_app_handling_exceptions(app, request)(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 53, in wrapped_app\n    raise exc\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 42, in wrapped_app\n    await app(scope, receive, sender)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 75, in app\n    response = await f(request)\n               ^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/fastapi/routing.py\", line 302, in app\n    raw_response = await run_endpoint_function(\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    ...<3 lines>...\n    )\n    ^\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/fastapi/routing.py\", line 213, in run_endpoint_function\n    return await dependant.call(**values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/app/api/v1/endpoints/fishing_locations.py\", line 55, in search_fishing_locations\n    locations, pagination = fishing_location_service.search_fishing_locations(db, search_params)\n                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/app/services/fishing_location_service.py\", line 39, in search_fishing_locations\n    locations = fishing_location.search(\n        db,\n    ...<7 lines>...\n        limit=search_params.page_size\n    )\n  File \"/Users/<USER>/cdc/luyabaike/api/app/crud/fishing_location.py\", line 59, in search\n    return db.exec(statement).all()\n           ^^^^^^^\nAttributeError: 'generator' object has no attribute 'exec'", "taskName": "Task-7"}
{"asctime": "2025-07-18 12:28:24,061", "name": "app.core.exceptions", "levelname": "ERROR", "message": "Unexpected Error: 'generator' object has no attribute 'exec'", "exc_info": "Traceback (most recent call last):\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py\", line 164, in __call__\n    await self.app(scope, receive, _send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py\", line 85, in __call__\n    await self.app(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/exceptions.py\", line 63, in __call__\n    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 53, in wrapped_app\n    raise exc\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 42, in wrapped_app\n    await app(scope, receive, sender)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 716, in __call__\n    await self.middleware_stack(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 736, in app\n    await route.handle(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 290, in handle\n    await self.app(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 78, in app\n    await wrap_app_handling_exceptions(app, request)(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 53, in wrapped_app\n    raise exc\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 42, in wrapped_app\n    await app(scope, receive, sender)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 75, in app\n    response = await f(request)\n               ^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/fastapi/routing.py\", line 302, in app\n    raw_response = await run_endpoint_function(\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    ...<3 lines>...\n    )\n    ^\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/fastapi/routing.py\", line 213, in run_endpoint_function\n    return await dependant.call(**values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/app/api/v1/endpoints/fishing_locations.py\", line 55, in search_fishing_locations\n    locations, pagination = fishing_location_service.search_fishing_locations(db, search_params)\n                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/app/services/fishing_location_service.py\", line 39, in search_fishing_locations\n    locations = fishing_location.search(\n        db,\n    ...<7 lines>...\n        limit=search_params.page_size\n    )\n  File \"/Users/<USER>/cdc/luyabaike/api/app/crud/fishing_location.py\", line 59, in search\n    return db.exec(statement).all()\n           ^^^^^^^\nAttributeError: 'generator' object has no attribute 'exec'", "taskName": "Task-3"}
{"asctime": "2025-07-18 12:28:24,092", "name": "uvicorn.error", "levelname": "ERROR", "message": "Exception in ASGI application\n", "exc_info": "Traceback (most recent call last):\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/uvicorn/protocols/http/httptools_impl.py\", line 409, in run_asgi\n    result = await app(  # type: ignore[func-returns-value]\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n        self.scope, self.receive, self.send\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/uvicorn/middleware/proxy_headers.py\", line 60, in __call__\n    return await self.app(scope, receive, send)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/fastapi/applications.py\", line 1054, in __call__\n    await super().__call__(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/applications.py\", line 113, in __call__\n    await self.middleware_stack(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py\", line 186, in __call__\n    raise exc\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py\", line 164, in __call__\n    await self.app(scope, receive, _send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py\", line 85, in __call__\n    await self.app(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/middleware/exceptions.py\", line 63, in __call__\n    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 53, in wrapped_app\n    raise exc\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 42, in wrapped_app\n    await app(scope, receive, sender)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 716, in __call__\n    await self.middleware_stack(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 736, in app\n    await route.handle(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 290, in handle\n    await self.app(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 78, in app\n    await wrap_app_handling_exceptions(app, request)(scope, receive, send)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 53, in wrapped_app\n    raise exc\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py\", line 42, in wrapped_app\n    await app(scope, receive, sender)\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/starlette/routing.py\", line 75, in app\n    response = await f(request)\n               ^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/fastapi/routing.py\", line 302, in app\n    raw_response = await run_endpoint_function(\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    ...<3 lines>...\n    )\n    ^\n  File \"/Users/<USER>/cdc/luyabaike/api/.venv/lib/python3.13/site-packages/fastapi/routing.py\", line 213, in run_endpoint_function\n    return await dependant.call(**values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/app/api/v1/endpoints/fishing_locations.py\", line 55, in search_fishing_locations\n    locations, pagination = fishing_location_service.search_fishing_locations(db, search_params)\n                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/cdc/luyabaike/api/app/services/fishing_location_service.py\", line 39, in search_fishing_locations\n    locations = fishing_location.search(\n        db,\n    ...<7 lines>...\n        limit=search_params.page_size\n    )\n  File \"/Users/<USER>/cdc/luyabaike/api/app/crud/fishing_location.py\", line 59, in search\n    return db.exec(statement).all()\n           ^^^^^^^\nAttributeError: 'generator' object has no attribute 'exec'", "taskName": "Task-3"}
